#!/bin/bash

# Claude Code Router Startup Script
# This ensures the correct environment is loaded

echo "🚀 Starting Claude Code Router..."

# Load shell configuration (handles conda conflicts)
source ~/.zshrc

# Verify correct Node.js version
echo "📋 Using Node.js $(node --version) and Claude Code $(claude --version | head -1)"

# Check if router is running
echo "📊 Checking router status..."
if ! ccr status | grep -q "✅ Status: Running"; then
    echo "🔄 Starting router..."
    ccr start
    sleep 2
fi

# Start Claude Code
echo "🤖 Starting Claude Code..."
ccr code

echo "✅ Done!"
