#!/bin/bash

# Claude Code Router Startup Script
# This ensures the correct environment is loaded

echo "🚀 Starting Claude Code Router..."

# Load NVM and use Node.js v22
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 22 > /dev/null 2>&1

# Check if router is running
echo "📊 Checking router status..."
ccr status

# If not running, start it
if ! ccr status | grep -q "✅ Status: Running"; then
    echo "🔄 Starting router..."
    ccr start
    sleep 2
fi

# Start Claude Code
echo "🤖 Starting Claude Code..."
ccr code

echo "✅ Done!"
