{"LOG": true, "API_TIMEOUT_MS": 600000, "NON_INTERACTIVE_MODE": false, "Providers": [{"name": "anthropic", "api_base_url": "https://api.anthropic.com/v1/messages", "api_key": "your-anthropic-api-key-here", "models": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022"], "transformer": {"use": ["anthropic"]}}, {"name": "openai", "api_base_url": "https://api.openai.com/v1/chat/completions", "api_key": "your-openai-api-key-here", "models": ["gpt-4o", "gpt-4o-mini"]}, {"name": "siliconflow", "api_base_url": "https://api.siliconflow.cn/v1/chat/completions", "api_key": "sk-pbucaplrxeotmnzgtodjtjndgbltxdgdxnmpctxaquzfjrge", "models": ["moonshotai/Kimi-K2-Instruct", "deepseek-ai/DeepSeek-V3"], "transformer": {"use": [["maxtoken", {"max_tokens": 16384}], "enhancetool"]}}, {"name": "openrouter", "api_base_url": "https://openrouter.ai/api/v1/chat/completions", "api_key": "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", "models": ["moonshotai/kimi-k2", "google/gemini-2.5-flash", "google/gemini-2.5-flash-lite", "deepseek/deepseek-r1-0528:free"], "transformer": {"use": ["openrouter"], "deepseek/deepseek-r1-0528:free": {"use": ["openrouter", "enhancetool"]}}}], "Router": {"default": "siliconflow,moonshotai/Kimi-K2-Instruct", "background": "openrouter,deepseek/deepseek-r1-0528:free", "think": "siliconflow,deepseek-ai/DeepSeek-V3", "longContext": "openrouter,google/gemini-2.5-flash", "longContextThreshold": 60000, "webSearch": "openrouter,google/gemini-2.5-flash"}}