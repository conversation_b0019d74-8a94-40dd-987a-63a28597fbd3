<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voxel World - Minecraft-like</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
        }
        #crosshair::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 2px;
            background: white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
        }
        #crosshair::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            height: 20px;
            width: 2px;
            background: white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
        }
        #ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            pointer-events: none;
            text-shadow: 1px 1px 2px black;
        }
        #controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            pointer-events: none;
            text-shadow: 1px 1px 2px black;
        }
        #inventory {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }
        .inventory-slot {
            width: 40px;
            height: 40px;
            border: 2px solid white;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }
        .inventory-slot.active {
            border-color: yellow;
            background: rgba(255, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="crosshair"></div>
        <div id="ui">
            <div>Voxel World</div>
            <div>Blocks: <span id="blockCount">0</span></div>
            <div>FPS: <span id="fpsCounter">0</span></div>
        </div>
        <div id="controls">
            <div>WASD: Move | Mouse: Look | Left Click: Remove | Right Click: Place</div>
            <div>1-9: Select Block | F: Fly Mode | R: Reset Position</div>
        </div>
        <div id="inventory">
            <div class="inventory-slot active">🟢</div>
            <div class="inventory-slot">🟤</div>
            <div class="inventory-slot">🟫</div>
            <div class="inventory-slot">🟩</div>
            <div class="inventory-slot">🟥</div>
            <div class="inventory-slot">🟦</div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/PointerLockControls.min.js"></script>
    <script>
        class VoxelGame {
            constructor() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x87CEEB); // Sky blue
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                document.getElementById('gameContainer').appendChild(this.renderer.domElement);

                // Game state
                this.voxels = new Map();
                this.selectedBlockType = 'grass';
                this.flyMode = false;
                this.playerSpeed = 0.1;
                this.gravity = -0.01;
                this.velocityY = 0;
                this.isGrounded = false;
                this.blockCount = 0;

                // Controls
                this.controls = new THREE.PointerLockControls(this.camera, this.renderer.domElement);
                this.keys = {};
                this.mouseDown = false;
                this.intersectedBlock = null;

                // Block types with material properties
                this.blockTypes = {
                    grass: { color: 0x4CAF50, name: 'Grass' },
                    dirt: { color: 0x795548, name: 'Dirt' },
                    stone: { color: 0x9E9E9E, name: 'Stone' },
                    wood: { color: 0x8B4513, name: 'Wood' },
                    leaf: { color: 0x2E7D32, name: 'Leaf' },
                    water: { color: 0x2196F3, name: 'Water' },
                    sand: { color: 0xFFD54F, name: 'Sand' }
                };

                this.init();
                this.setupEventListeners();
                this.gameLoop();
            }

            init() {
                this.createWorld();
                this.createLighting();
                this.setupPlayer();
            }

            createWorld() {
                // Create ground
                for (let x = -10; x <= 10; x++) {
                    for (let z = -10; z <= 10; z++) {
                        // Create terrain height
                        const height = Math.floor(3 + Math.sin(x * 0.2) * Math.cos(z * 0.3) * 2);
                        
                        // Create ground layers
                        for (let y = 0; y <= height; y++) {
                            if (y === height) {
                                this.setVoxel(x, y, z, 'grass');
                            } else if (y > height - 3) {
                                this.setVoxel(x, y, z, 'dirt');
                            } else {
                                this.setVoxel(x, y, z, 'stone');
                            }
                        }
                        
                        // Create some trees
                        if (Math.random() > 0.95 && height > 2) {
                            this.createTree(x, height + 1, z);
                        }
                    }
                }

                // Add some sand near water
                for (let x = -5; x <= 5; x++) {
                    for (let z = -5; z <= 5; z++) {
                        if (Math.random() > 0.9) {
                            this.setVoxel(x, 0, z, 'sand');
                        }
                    }
                }
                
                // Add water bodies
                for (let x = -3; x <= 3; x++) {
                    for (let z = -3; z <= 3; z++) {
                        this.setVoxel(x, 0, z, 'water');
                    }
                }
            }

            createTree(x, y, z) {
                // Trunk
                for (let i = 0; i < 4; i++) {
                    this.setVoxel(x, y + i, z, 'wood');
                }
                
                // Leaves
                for (let lx = -2; lx <= 2; lx++) {
                    for (let lz = -2; lz <= 2; lz++) {
                        for (let ly = 0; ly <= 2; ly++) {
                            if (Math.abs(lx) + Math.abs(lz) + ly <= 4) {
                                const leafX = x + lx;
                                const leafY = y + 4 + ly;
                                const leafZ = z + lz;
                                
                                // Make sure we don't overwrite existing blocks
                                if (!this.getVoxel(leafX, leafY, leafZ)) {
                                    this.setVoxel(leafX, leafY, leafZ, 'leaf');
                                }
                            }
                        }
                    }
                }
            }

            createLighting() {
                // Ambient light
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                this.scene.add(ambientLight);

                // Directional light (sun)
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 20, 10);
                directionalLight.castShadow = true;
                directionalLight.shadow.mapSize.width = 1024;
                directionalLight.shadow.mapSize.height = 1024;
                this.scene.add(directionalLight);
            }

            setupPlayer() {
                // Start player at a good position
                this.camera.position.set(0, 10, 0);
                
                // Add event listener to lock pointer
                const button = document.createElement('button');
                button.textContent = 'Click to Start';
                button.style.position = 'absolute';
                button.style.top = '50%';
                button.style.left = '50%';
                button.style.transform = 'translate(-50%, -50%)';
                button.style.zIndex = '1000';
                button.style.padding = '10px 20px';
                button.style.fontSize = '18px';
                button.style.cursor = 'pointer';
                document.getElementById('gameContainer').appendChild(button);
                
                button.addEventListener('click', () => {
                    document.body.requestPointerLock = document.body.requestPointerLock || document.body.mozRequestPointerLock;
                    document.body.requestPointerLock();
                    button.remove();
                });
            }

            setupEventListeners() {
                // Keyboard events
                window.addEventListener('keydown', (e) => {
                    this.keys[e.key.toLowerCase()] = true;
                    
                    // Block selection with number keys
                    if (e.key >= '1' && e.key <= '9') {
                        const types = Object.keys(this.blockTypes);
                        const index = parseInt(e.key) - 1;
                        if (index < types.length) {
                            this.selectedBlockType = types[index];
                            this.updateInventoryDisplay();
                        }
                    }
                    
                    // Toggle fly mode
                    if (e.key.toLowerCase() === 'f') {
                        this.flyMode = !this.flyMode;
                    }
                    
                    // Reset position
                    if (e.key.toLowerCase() === 'r') {
                        this.camera.position.set(0, 10, 0);
                    }
                });

                window.addEventListener('keyup', (e) => {
                    this.keys[e.key.toLowerCase()] = false;
                });

                // Mouse events
                window.addEventListener('mousedown', (e) => {
                    this.mouseDown = true;
                    if (e.button === 0) { // Left click - remove block
                        this.removeBlock();
                    } else if (e.button === 2) { // Right click - place block
                        this.placeBlock();
                    }
                });

                window.addEventListener('mouseup', () => {
                    this.mouseDown = false;
                });

                // Mouse move for camera control
                window.addEventListener('mousemove', (e) => {
                    if (document.pointerLockElement === document.body) {
                        this.camera.rotation.y -= e.movementX * 0.002;
                        this.camera.rotation.x -= e.movementY * 0.002;
                        this.camera.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.camera.rotation.x));
                    }
                });

                // Window resize
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });

                // Inventory selection
                document.querySelectorAll('.inventory-slot').forEach((slot, index) => {
                    slot.addEventListener('click', () => {
                        const types = Object.keys(this.blockTypes);
                        if (index < types.length) {
                            this.selectedBlockType = types[index];
                            this.updateInventoryDisplay();
                        }
                    });
                });
            }

            updateInventoryDisplay() {
                document.querySelectorAll('.inventory-slot').forEach((slot, index) => {
                    const types = Object.keys(this.blockTypes);
                    if (index < types.length && types[index] === this.selectedBlockType) {
                        slot.classList.add('active');
                    } else {
                        slot.classList.remove('active');
                    }
                });
            }

            getVoxel(x, y, z) {
                const key = `${x},${y},${z}`;
                return this.voxels.get(key);
            }

            setVoxel(x, y, z, type) {
                const key = `${x},${y},${z}`;
                this.voxels.set(key, { type, x, y, z });
                this.blockCount++;
                document.getElementById('blockCount').textContent = this.blockCount;
                return true;
            }

            removeVoxel(x, y, z) {
                const key = `${x},${y},${z}`;
                if (this.voxels.has(key)) {
                    this.voxels.delete(key);
                    this.blockCount--;
                    document.getElementById('blockCount').textContent = this.blockCount;
                    return true;
                }
                return false;
            }

            placeBlock() {
                if (!this.intersectedBlock) return;
                
                const { x, y, z } = this.intersectedBlock;
                // Place block in front of the intersected block
                const newX = x + this.getDirectionVector().x;
                const newY = y + this.getDirectionVector().y;
                const newZ = z + this.getDirectionVector().z;
                
                if (!this.getVoxel(newX, newY, newZ)) {
                    this.setVoxel(newX, newY, newZ, this.selectedBlockType);
                }
            }

            removeBlock() {
                if (this.intersectedBlock) {
                    const { x, y, z } = this.intersectedBlock;
                    this.removeVoxel(x, y, z);
                }
            }

            getDirectionVector() {
                const direction = new THREE.Vector3();
                this.camera.getWorldDirection(direction);
                return new THREE.Vector3(
                    Math.round(direction.x),
                    Math.round(direction.y),
                    Math.round(direction.z)
                );
            }

            updatePlayer(delta) {
                // Movement
                const moveSpeed = this.flyMode ? 0.2 : 0.1;
                
                if (this.keys['w']) {
                    this.moveCamera(moveSpeed, 0, 0);
                }
                if (this.keys['s']) {
                    this.moveCamera(-moveSpeed, 0, 0);
                }
                if (this.keys['a']) {
                    this.moveCamera(0, 0, moveSpeed);
                }
                if (this.keys['d']) {
                    this.moveCamera(0, 0, -moveSpeed);
                }
                
                // Flying mode
                if (this.flyMode) {
                    if (this.keys[' ']) {
                        this.camera.position.y += moveSpeed * 10;
                    }
                    if (this.keys['shift']) {
                        this.camera.position.y -= moveSpeed * 10;
                    }
                } else {
                    // Gravity
                    if (!this.isGrounded) {
                        this.velocityY += this.gravity;
                        this.camera.position.y += this.velocityY;
                    }
                }
                
                // Check ground collision if not in fly mode
                if (!this.flyMode) {
                    const playerY = this.camera.position.y;
                    const groundY = 0;
                    if (playerY <= groundY) {
                        this.camera.position.y = groundY;
                        this.velocityY = 0;
                        this.isGrounded = true;
                    } else {
                        this.isGrounded = false;
                    }
                }
            }

            moveCamera(x, y, z) {
                const direction = new THREE.Vector3();
                this.camera.getWorldDirection(direction);
                direction.y = 0;
                direction.normalize();
                
                const right = new THREE.Vector3();
                right.crossVectors(direction, new THREE.Vector3(0, 1, 0)).normalize();
                
                this.camera.position.x += (x * direction.x + z * right.x) * this.playerSpeed;
                this.camera.position.z += (x * direction.z + z * right.z) * this.playerSpeed;
            }

            checkBlockIntersections() {
                const raycaster = new THREE.Raycaster();
                raycaster.setFromCamera(new THREE.Vector2(), this.camera);
                
                const intersects = raycaster.intersectObjects(this.scene.children);
                
                if (intersects.length > 0) {
                    const intersect = intersects[0];
                    const normal = intersect.face.normal;
                    const blockPos = new THREE.Vector3(
                        Math.round(intersect.point.x - normal.x * 0.5),
                        Math.round(intersect.point.y - normal.y * 0.5),
                        Math.round(intersect.point.z - normal.z * 0.5)
                    );
                    this.intersectedBlock = {
                        x: blockPos.x,
                        y: blockPos.y,
                        z: blockPos.z
                    };
                } else {
                    this.intersectedBlock = null;
                }
            }

            render() {
                // Update FPS display
                const now = performance.now();
                let fps = 0;
                if (this.lastFrameTime) {
                    fps = Math.round(1000 / (now - this.lastFrameTime));
                }
                this.lastFrameTime = now;
                document.getElementById('fpsCounter').textContent = fps;
                
                // Update rendering
                this.checkBlockIntersections();
                this.renderer.render(this.scene, this.camera);
            }

            gameLoop() {
                const delta = 1/60;
                
                this.updatePlayer(delta);
                
                // Render the scene
                this.render();
                
                // Continue the loop
                requestAnimationFrame(() => this.gameLoop());
            }
        }

        // Start the game when the page loads
        window.addEventListener('load', () => {
            new VoxelGame();
        });
    </script>
</body>
</html>