# Claude Code Router - API Switching Guide

## Key Installation Issues Encountered

1. **Node.js Missing**: Had to install Node.js v22+ via NVM
2. **Claude Code Corruption**: Homebrew installation was broken, needed npm reinstall
3. **Version Mismatch**: Claude Code Router requires Node.js v20+, was using v18
4. **PATH Issues**: Commands not found after installation

## Quick Setup for New API Providers

### 1. Update Configuration

Edit your config file:
```bash
nano ~/.claude-code-router/config.json
```

Replace the content with this configuration:

```json
{
  "LOG": true,
  "API_TIMEOUT_MS": 600000,
  "NON_INTERACTIVE_MODE": false,
  "Providers": [
    {
      "name": "openrouter",
      "api_base_url": "https://openrouter.ai/api/v1/chat/completions",
      "api_key": "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",
      "models": ["z-ai/glm-4.5v", "moonshotai/kimi-k2:free"],
      "transformer": {
        "use": ["openrouter"]
      }
    },
    {
      "name": "siliconflow",
      "api_base_url": "https://api.siliconflow.cn/v1/chat/completions",
      "api_key": "sk-pbucaplrxeotmnzgtodjtjndgbltxdgdxnmpctxaquzfjrge",
      "models": ["Qwen/Qwen3-Coder-30B-A3B-Instruct", "Qwen/Qwen3-Coder-480B-A35B-Instruct"]
    }
  ],
  "Router": {
    "default": "openrouter,z-ai/glm-4.5v",
    "background": "siliconflow,Qwen/Qwen3-Coder-30B-A3B-Instruct",
    "think": "siliconflow,Qwen/Qwen3-Coder-480B-A35B-Instruct",
    "longContext": "siliconflow,Qwen/Qwen3-Coder-480B-A35B-Instruct",
    "longContextThreshold": 60000
  }
}
```

### 2. Restart Router

```bash
ccr restart
```

### 3. Start Claude Code Session

```bash
ccr code
```

### 4. Switch Models During Chat

Use these commands **inside the Claude Code session**:

```bash
# Switch to OpenRouter GLM-4.5v
/model openrouter,z-ai/glm-4.5v

# Switch to OpenRouter Kimi (free)
/model openrouter,moonshotai/kimi-k2:free

# Switch to SiliconFlow Qwen 30B
/model siliconflow,Qwen/Qwen3-Coder-30B-A3B-Instruct

# Switch to SiliconFlow Qwen 480B
/model siliconflow,Qwen/Qwen3-Coder-480B-A35B-Instruct
```

### 5. Test Setup

```bash
# Check router status
ccr status

# Start coding session with a prompt
ccr code "Hello, which model am I using?"

# Or start interactive session
ccr code
```

## Model Recommendations

- **Default**: `z-ai/glm-4.5v` (OpenRouter) - Good general purpose
- **Background Tasks**: `Qwen3-Coder-30B` (SiliconFlow) - Faster, cost-effective
- **Complex Coding**: `Qwen3-Coder-480B` (SiliconFlow) - Most capable
- **Free Option**: `moonshotai/kimi-k2:free` (OpenRouter) - No cost

## Troubleshooting

If commands don't work:
```bash
# Ensure correct Node.js version
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm use 22

# Check router logs
tail -f ~/.claude-code-router.log
```

## Quick Commands Reference

```bash
ccr status    # Check if running
ccr start     # Start router
ccr stop      # Stop router
ccr restart   # Restart router
ccr code      # Start Claude Code
ccr ui        # Web interface
```
