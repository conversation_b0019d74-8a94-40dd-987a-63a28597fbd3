# Claude Code Router Setup

## Installation Complete! ✅

Claude Code Router has been successfully installed on your system.

## What was installed:

1. **Node.js v22.18.0** via NVM (Node Version Manager)
2. **Claude Code Router v1.0.37** globally via npm
3. **Configuration file** at `~/.claude-code-router/config.json`
4. **Wrapper script** `ccr-wrapper.sh` for easy usage

## Next Steps:

### 1. Configure API Keys

Edit the configuration file to add your API keys:

```bash
nano ~/.claude-code-router/config.json
```

Replace the placeholder API keys:
- `your-anthropic-api-key-here` - Get from https://console.anthropic.com/
- `your-openai-api-key-here` - Get from https://platform.openai.com/

### 2. Start the Router

Use the wrapper script to start the router:

```bash
./ccr-wrapper.sh start
```

Or use the direct command (make sure to load NVM first):

```bash
export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh" && nvm use 22 && ccr start
```

### 3. Run Claude Code with Router

Once the router is running, start Claude Code:

```bash
./ccr-wrapper.sh code
```

### 4. Check Status

To check if the router is running:

```bash
./ccr-wrapper.sh status
```

### 5. Use the Web UI (Optional)

For a graphical interface to manage configuration:

```bash
./ccr-wrapper.sh ui
```

## Available Commands:

- `ccr start` - Start the router server
- `ccr stop` - Stop the router server  
- `ccr restart` - Restart the router server
- `ccr status` - Show server status
- `ccr code` - Execute Claude Code with routing
- `ccr ui` - Open web UI for configuration
- `ccr -v` - Show version
- `ccr -h` - Show help

## Configuration:

The configuration file supports:
- Multiple AI providers (Anthropic, OpenAI, DeepSeek, Gemini, etc.)
- Model routing based on task type (default, background, thinking, long context)
- Request/response transformers
- Environment variable interpolation for API keys

## Troubleshooting:

If you encounter issues:

1. Make sure Node.js v22 is active: `nvm use 22`
2. Check the log file: `tail -f ~/.claude-code-router.log`
3. Verify configuration: `./ccr-wrapper.sh status`

## Documentation:

For more advanced configuration options, see:
- https://github.com/musistudio/claude-code-router
- Configuration examples in the repository
